'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface ProgressBarProps {
  isVisible: boolean;
  message?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ 
  isVisible, 
  message = "Weaving arcane magic..." 
}) => {
  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="absolute top-0 left-0 w-full z-10"
    >
      {/* Progress Bar Container */}
      <div className="relative h-1 bg-arcane-obsidian/50 overflow-hidden">
        {/* Animated Gradient Bar */}
        <div className="absolute inset-0 bg-gradient-to-r from-arcane-violet via-arcane-cyan to-arcane-orange">
          <div className="h-full w-full bg-gradient-to-r from-arcane-violet via-arcane-cyan to-arcane-orange animate-progress-slide"></div>
        </div>
        
        {/* Shimmer Effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer"></div>
      </div>

      {/* Progress Message */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="absolute top-2 left-1/2 transform -translate-x-1/2 bg-arcane-obsidian/80 backdrop-blur-md rounded-lg px-4 py-2 border border-arcane-violet/30"
      >
        <div className="flex items-center space-x-3">
          {/* Spinning Rune */}
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-4 h-4 text-arcane-violet"
          >
            <svg viewBox="0 0 24 24" fill="currentColor" className="w-full h-full">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" />
              <path d="M12 8L12.5 10.5L15 11L12.5 11.5L12 14L11.5 11.5L9 11L11.5 10.5L12 8Z" />
            </svg>
          </motion.div>
          
          {/* Message Text */}
          <span className="font-manrope text-sm text-white/90 font-medium">
            {message}
          </span>
          
          {/* Pulsing Dots */}
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
                className="w-1 h-1 bg-arcane-cyan rounded-full"
              />
            ))}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ProgressBar;
