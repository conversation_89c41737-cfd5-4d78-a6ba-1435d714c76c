"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { tv } from 'tailwind-variants';

interface StyleCardProps {
  styleName: string;
  isSelected: boolean;
  onSelect: (style: string) => void;
}

const cardVariants = tv({
  base: 'relative flex flex-col items-center justify-center p-2 cursor-pointer transition-all duration-200 ease-in-out border backdrop-blur-sm min-h-[60px] group overflow-hidden',
  variants: {
    selected: {
      true: 'bg-gradient-to-br from-arcane-primary to-arcane-accent text-white shadow-md shadow-arcane-primary/30 border-arcane-primary',
      false: 'bg-gradient-to-br from-purple-900/20 to-black/40 text-arcane-light border-arcane-primary/30 hover:bg-gradient-to-br hover:from-arcane-primary/15 hover:to-arcane-accent/15 hover:border-arcane-accent/50',
    },
  },
});

// Style icons mapping
const getStyleIcon = (styleName: string) => {
  const iconMap: { [key: string]: string } = {
    'Mystic Veil': '🌙',
    'Shadow Weave': '🌑',
    'Arcane Glyph': '✨',
    'Eldritch Rune': '🔮',
    'Celestial Bloom': '🌸',
    'Nebula Shroud': '🌌',
    'Abyssal Echo': '🌊',
    'Lunar Halo': '🌕',
    'Solar Flare': '☀️',
    'Void Tracer': '⚫',
    'Dream Weaver': '💫',
    'Phoenix Ember': '🔥',
    'Frostbite Sigil': '❄️',
    'Thunderstorm Crest': '⚡',
    'Enchanted Vine': '🌿'
  };
  return iconMap[styleName] || '✨';
};

const StyleCard: React.FC<StyleCardProps> = ({ styleName, isSelected, onSelect }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={cardVariants({ selected: isSelected })}
      onClick={() => onSelect(styleName)}
    >
      {/* Subtle glow effect for selected cards */}
      {isSelected && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12"></div>
      )}

      <div className="relative z-10 flex flex-col items-center space-y-1">
        <span className="text-lg">{getStyleIcon(styleName)}</span>
        <span className={`text-xs font-medium font-arcane text-center leading-tight transition-colors duration-200 ${
          isSelected ? 'text-white' : 'text-arcane-light/80 group-hover:text-arcane-accent'
        }`}>
          {styleName}
        </span>
      </div>

      {/* Selection indicator */}
      {isSelected && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute top-1 right-1 w-3 h-3 bg-white flex items-center justify-center"
        >
          <svg className="w-2 h-2 text-arcane-primary" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </motion.div>
      )}
    </motion.div>
  );
};

export default StyleCard;