"use client";
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion } from 'framer-motion';
import { ImageUp, Wand2, Loader2 } from 'lucide-react';
import Image from 'next/image'; // Added import for next/image
import StyleCard from './StyleCard';
import {  } from '../lib/supabase'; // Corrected import path

const styles = [
  'Mystic Veil', 'Shadow Weave', 'Arcane Glyph', 'Eldritch Rune',
  'Celestial Bloom', 'Nebula Shroud', 'Abyssal Echo', 'Lunar Halo',
  'Solar Flare', 'Void Tracer', 'Dream Weaver', 'Phoenix Ember',
  'Frostbite Sigil', 'Thunderstorm Crest', 'Enchanted Vine'
];

const UploadZone: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedStyle, setSelectedStyle] = useState<string | null>(null);
  const [transformedImage, setTransformedImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setSelectedFile(acceptedFiles[0]);
    setTransformedImage(null);
    setError(null);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': [],
      'image/png': [],
      'image/webp': [],
    },
    multiple: false,
  });

  const handleTransform = async () => {
    if (!selectedFile || !selectedStyle) {
      setError('Please select an image and a style.');
      return;
    }

    setLoading(true);
    setError(null);
    setTransformedImage(null);

    try {
      // // Upload original image to Supabase
      // const originalImageUrl = await uploadImage(selectedFile);

      const formData = new FormData();
      formData.append('image', selectedFile);
      formData.append('style', selectedStyle);

      const response = await fetch('/api/generate', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Image transformation failed.');
      }

      const data = await response.json();

      if (data.error) {
        // Handle case where API returns an error with fallback
        if (data.fallback) {
          setTransformedImage(data.fallback);
          setError(`Image generation failed: ${data.error}. Showing fallback image.`);
        } else {
          throw new Error(data.error);
        }
      } else {
        // Successfully generated image (data URL or regular URL)
        const transformedImageUrl = data.image;
        setTransformedImage(transformedImageUrl);
      }

      // // Save record to database (assuming a dummy user ID for now)
      // await saveImageRecord('dummy-user-id', originalImageUrl, transformedImageUrl, selectedStyle);

    } catch (err) {
      setError((err as Error).message || 'An unexpected error occurred.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <section id="upload-section" className="py-8 px-4 lg:px-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 15 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-8"
        >
          <h2 className="text-2xl lg:text-3xl font-arcane font-bold mb-2 bg-gradient-to-r from-arcane-primary to-arcane-accent bg-clip-text text-transparent">
            AI Style Transfer
          </h2>
          <p className="text-sm text-arcane-light/70 max-w-xl mx-auto">
            Upload an image and select a mystical style for AI transformation
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Upload Section */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-4"
          >
            <div className="bg-gradient-to-br from-purple-900/30 to-black/50 backdrop-blur-sm border border-arcane-primary/30 p-4 shadow-lg hover:shadow-arcane-primary/20 transition-all duration-300">
              <div className="mb-4">
                <h3 className="text-lg font-arcane font-semibold text-arcane-accent mb-1">Upload Image</h3>
                <p className="text-arcane-light/60 text-xs">Select your source image</p>
              </div>

              <div
                {...getRootProps()}
                className={`border border-dashed text-center cursor-pointer transition-all duration-300 min-h-[200px] flex flex-col items-center justify-center p-4 ${
                  isDragActive
                    ? 'border-arcane-primary bg-arcane-primary/10'
                    : 'border-arcane-primary/40 hover:border-arcane-accent hover:bg-arcane-accent/5'
                }`}
              >
                <input {...getInputProps()} />
                <ImageUp className={`w-12 h-12 mb-3 transition-colors duration-300 ${
                  isDragActive ? 'text-arcane-primary' : 'text-arcane-secondary'
                }`} />
                {selectedFile ? (
                  <div className="space-y-2">
                    <p className="text-sm text-arcane-light font-medium">✨ {selectedFile.name}</p>
                    <p className="text-xs text-arcane-light/60">Click to change</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <p className="text-sm text-arcane-light/80 font-medium">Drop image here</p>
                    <p className="text-xs text-arcane-light/60">or click to browse</p>
                    <p className="text-xs text-arcane-light/40">JPG, PNG, WebP</p>
                  </div>
                )}
              </div>

              {selectedFile && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="mt-4"
                >
                  <div className="relative overflow-hidden border border-arcane-primary/20">
                    <Image
                      src={URL.createObjectURL(selectedFile)}
                      alt="Selected"
                      width={300}
                      height={200}
                      className="w-full h-32 object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                  </div>
                </motion.div>
              )}
            </div>
          </motion.div>

          {/* Style Selection */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="space-y-4"
          >
            <div className="bg-gradient-to-br from-purple-900/30 to-black/50 backdrop-blur-sm border border-arcane-primary/30 p-4 shadow-lg hover:shadow-arcane-primary/20 transition-all duration-300">
              <div className="mb-4">
                <h3 className="text-lg font-arcane font-semibold text-arcane-accent mb-1">Style Selection</h3>
                <p className="text-arcane-light/60 text-xs">Choose transformation style</p>
                {selectedStyle && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="mt-2 inline-block px-3 py-1 bg-arcane-primary/20 border border-arcane-primary/40 text-xs"
                  >
                    <span className="text-arcane-primary font-medium">✨ {selectedStyle}</span>
                  </motion.div>
                )}
              </div>

              <div className="grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 gap-2 max-h-[300px] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-arcane-primary scrollbar-track-transparent">
                {styles.map((style, index) => (
                  <motion.div
                    key={style}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.02 }}
                  >
                    <StyleCard
                      styleName={style}
                      isSelected={selectedStyle === style}
                      onSelect={setSelectedStyle}
                    />
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Action Button & Result */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mt-6"
        >
          {/* Error Message */}
          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="mb-4"
            >
              <div className="bg-red-900/30 border border-red-500/40 p-3 backdrop-blur-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-red-500 flex items-center justify-center">
                    <span className="text-white text-xs">!</span>
                  </div>
                  <p className="text-red-300 text-sm">{error}</p>
                </div>
              </div>
            </motion.div>
          )}

          {/* Transform Button */}
          <div className="text-center mb-6">
            <motion.button
              onClick={handleTransform}
              disabled={loading || !selectedFile || !selectedStyle}
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
              className="bg-gradient-to-r from-arcane-primary to-arcane-accent hover:from-arcane-accent hover:to-arcane-primary disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-semibold py-3 px-8 shadow-lg shadow-arcane-primary/20 transition-all duration-300 flex items-center justify-center mx-auto text-sm border border-arcane-primary/30"
            >
              {loading ? (
                <>
                  <Loader2 className="animate-spin mr-2 w-4 h-4" />
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <Wand2 className="mr-2 w-4 h-4" />
                  <span>Transform</span>
                </>
              )}
            </motion.button>

            {!selectedFile || !selectedStyle ? (
              <p className="text-arcane-light/50 mt-2 text-xs">
                {!selectedFile && !selectedStyle ? 'Upload image & select style' :
                 !selectedFile ? 'Upload an image' :
                 'Select a style'}
              </p>
            ) : null}
          </div>

          {/* Result Section */}
          {transformedImage && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="bg-gradient-to-br from-purple-900/30 to-black/50 backdrop-blur-sm border border-arcane-primary/30 p-4 shadow-lg"
            >
              <div className="mb-4">
                <h3 className="text-lg font-arcane font-semibold text-arcane-accent mb-1">✨ Result</h3>
                <p className="text-arcane-light/60 text-xs">Transformation complete</p>
              </div>

              <div className="relative">
                <div className="relative overflow-hidden border border-arcane-primary/30 shadow-lg">
                  <Image
                    src={transformedImage}
                    alt="Transformed"
                    width={600}
                    height={400}
                    className="w-full h-auto max-h-80 object-contain"
                    unoptimized={transformedImage.startsWith('data:')}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent"></div>
                </div>

                {/* Download Button */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="mt-3 text-center"
                >
                  <button
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = transformedImage;
                      link.download = `arcane-artify-${selectedStyle?.toLowerCase().replace(/\s+/g, '-')}.png`;
                      link.click();
                    }}
                    className="bg-gradient-to-r from-arcane-secondary to-arcane-accent hover:from-arcane-accent hover:to-arcane-secondary text-white font-medium py-2 px-4 shadow-md transition-all duration-300 inline-flex items-center space-x-2 text-sm border border-arcane-secondary/30"
                  >
                    <span>Download</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </button>
                </motion.div>
              </div>
            </motion.div>
          )}
        </motion.div>
      </div>
    </section>
  );
};

export default UploadZone;