"use client";
import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';

const Footer: React.FC = () => {
  return (
    <footer className="relative bg-gradient-to-br from-black via-purple-950/50 to-arcane-dark border-t border-arcane-primary/20">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 15 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-3"
          >
            <h3 className="text-lg font-arcane font-bold bg-gradient-to-r from-arcane-primary to-arcane-accent bg-clip-text text-transparent">
              Arcane Artify
            </h3>
            <p className="text-arcane-light/70 text-sm leading-relaxed">
              AI-powered mystical style transfers using Google Gemini 2.0 Flash.
            </p>
            <div className="flex space-x-2">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="w-8 h-8 bg-arcane-primary/20 flex items-center justify-center border border-arcane-primary/30 hover:bg-arcane-primary/30 transition-colors duration-300"
              >
                <span className="text-arcane-primary text-sm">✨</span>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="w-8 h-8 bg-arcane-accent/20 flex items-center justify-center border border-arcane-accent/30 hover:bg-arcane-accent/30 transition-colors duration-300"
              >
                <span className="text-arcane-accent text-sm">🌙</span>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="w-8 h-8 bg-arcane-secondary/20 flex items-center justify-center border border-arcane-secondary/30 hover:bg-arcane-secondary/30 transition-colors duration-300"
              >
                <span className="text-arcane-secondary text-sm">🔮</span>
              </motion.div>
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-4"
          >
            <h4 className="text-lg font-arcane font-semibold text-arcane-accent">Quick Links</h4>
            <div className="space-y-3">
              <Link href="/" className="block text-arcane-light/70 hover:text-arcane-primary transition-colors duration-200">
                Home
              </Link>
              <Link href="/gallery" className="block text-arcane-light/70 hover:text-arcane-primary transition-colors duration-200">
                Gallery
              </Link>
              <Link href="#upload-section" className="block text-arcane-light/70 hover:text-arcane-primary transition-colors duration-200">
                Transform
              </Link>
            </div>
          </motion.div>

          {/* Mystical Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-4"
          >
            <h4 className="text-lg font-arcane font-semibold text-arcane-accent">Mystical Powers</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <span className="text-arcane-primary">🎨</span>
                <span className="text-arcane-light/70">15 Unique Styles</span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-arcane-accent">⚡</span>
                <span className="text-arcane-light/70">AI-Powered Magic</span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-arcane-secondary">🌟</span>
                <span className="text-arcane-light/70">Instant Results</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="mt-6 pt-4 border-t border-arcane-primary/20 text-center"
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
            <p className="text-arcane-light/60 text-xs">
              © 2024 Arcane Artify. Powered by mystical AI technology.
            </p>
            <div className="flex items-center space-x-1 text-xs text-arcane-light/60">
              <span>Google Gemini 2.0 Flash</span>
              <span className="text-arcane-accent">✨</span>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          animate={{
            y: [0, -10, 0],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/4 left-1/6 w-4 h-4 bg-arcane-primary/30 rounded-full blur-sm"
        ></motion.div>
        <motion.div
          animate={{
            y: [0, 8, 0],
            opacity: [0.2, 0.5, 0.2]
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute bottom-1/3 right-1/4 w-3 h-3 bg-arcane-accent/40 rounded-full blur-sm"
        ></motion.div>
      </div>
    </footer>
  );
};

export default Footer;
