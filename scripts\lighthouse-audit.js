import lighthouse from 'lighthouse';
import * as chromeLauncher from 'chrome-launcher';

async function runLighthouseAudit() {
  const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] });
  const options = { logLevel: 'info', output: 'json', port: chrome.port };
  const runnerResult = await lighthouse('https://arcaneartify.vercel.app/', options);

  // .report is the JSON output as a string
  const report = JSON.parse(runnerResult.report);

  console.log('Lighthouse Audit Scores:');
  let allScoresAbove90 = true;

  for (const category in report.categories) {
    const score = report.categories[category].score * 100;
    console.log(`${report.categories[category].title}: ${score}`);
    if (score < 90) {
      allScoresAbove90 = false;
    }
  }

  await chrome.kill();

  if (!allScoresAbove90) {
    console.error('Lighthouse audit failed: One or more category scores are below 90.');
    process.exit(1);
  } else {
    console.log('Lighthouse audit passed: All category scores are 90 or above.');
  }
}

runLighthouseAudit();