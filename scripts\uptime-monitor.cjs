const axios = require('axios');
const { createClient } = require('@supabase/supabase-js');
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

(async () => {
  try {
    const response = await axios.get('https://arcane-artify.vercel.app/');
    const status = response.status === 200 ? 'up' : 'down';
    
    const { error } = await supabase
      .from('uptime_history')
      .insert({ status, timestamp: new Date() });

    if (error) console.error('Error saving uptime:', error);
    else console.log(`Uptime recorded: ${status}`);

    if (status === 'down') {
      console.error('Uptime alert: Arcane Artify is down!');
      // In a real scenario, integrate with a notification service (e.g., PagerDuty, Slack)
    }
  } catch (error) {
    console.error('Uptime check failed:', error.message);
    console.error('Uptime alert: Arcane Artify check failed!');
  }
})();