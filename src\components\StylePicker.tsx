'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { arcaneStyles, type StylePreset } from '../lib/styles';

interface StylePickerProps {
  selectedStyle: string | null;
  onStyleSelect: (styleId: string) => void;
}

const StyleCard: React.FC<{
  style: StylePreset;
  isSelected: boolean;
  onClick: () => void;
}> = ({ style, isSelected, onClick }) => {
  const [isFlipped, setIsFlipped] = useState(false);

  return (
    <motion.div
      className="relative w-64 h-40 md:w-48 md:h-32 lg:w-64 lg:h-40 cursor-pointer group"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      onHoverStart={() => setIsFlipped(true)}
      onHoverEnd={() => setIsFlipped(false)}
      role="option"
      aria-selected={isSelected}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      }}
    >
      <motion.div
        className="w-full h-full relative preserve-3d"
        animate={{ rotateY: isFlipped ? 180 : 0 }}
        transition={{ duration: 0.6, ease: "easeInOut" }}
        style={{ transformStyle: 'preserve-3d' }}
      >
        {/* Front Face */}
        <div
          className={`absolute inset-0 w-full h-full rounded-2xl border-2 p-4 flex flex-col justify-center items-center text-center backface-hidden ${
            isSelected
              ? 'border-arcane-violet bg-arcane-violet/20 shadow-arcane-2xl scale-105'
              : 'border-white/20 bg-white/10 hover:border-arcane-cyan'
          } backdrop-blur-lg transition-all duration-300 animate-chroma-border`}
          style={{ backfaceVisibility: 'hidden' }}
        >
          <h3 className="font-cinzel text-xl font-semibold text-white mb-2 tracking-arcane">
            {style.name}
          </h3>
          <div className="w-8 h-0.5 bg-gradient-to-r from-arcane-violet to-arcane-cyan mx-auto"></div>
        </div>

        {/* Back Face */}
        <div
          className="absolute inset-0 w-full h-full rounded-2xl border-2 border-arcane-cyan bg-arcane-cyan/20 backdrop-blur-lg p-4 flex flex-col justify-center items-center text-center backface-hidden"
          style={{ 
            backfaceVisibility: 'hidden',
            transform: 'rotateY(180deg)'
          }}
        >
          <p className="font-manrope text-sm text-arcane-cyan leading-relaxed">
            {style.description}
          </p>
        </div>
      </motion.div>

      {/* Focus Ring */}
      {isSelected && (
        <div className="absolute inset-0 rounded-2xl ring-2 ring-arcane-violet ring-offset-2 ring-offset-arcane-obsidian pointer-events-none"></div>
      )}
    </motion.div>
  );
};

const StylePicker: React.FC<StylePickerProps> = ({ selectedStyle, onStyleSelect }) => {
  return (
    <section className="py-12 px-4 lg:px-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <h2 className="font-cinzel text-3xl lg:text-4xl font-semibold mb-4 bg-gradient-to-r from-arcane-violet to-arcane-cyan bg-clip-text text-transparent tracking-arcane">
            Choose Your Arcane Style
          </h2>
          <p className="font-manrope text-base text-white/70 max-w-2xl mx-auto leading-arcane">
            Select from 15 mystical transformation styles, each imbued with unique magical properties
          </p>
        </motion.div>

        {/* Desktop: Horizontal Scroll */}
        <div className="hidden lg:block">
          <div className="overflow-x-auto pb-4">
            <div className="flex space-x-6 min-w-max px-4">
              {arcaneStyles.map((style, index) => (
                <motion.div
                  key={style.id}
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <StyleCard
                    style={style}
                    isSelected={selectedStyle === style.id}
                    onClick={() => onStyleSelect(style.id)}
                  />
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Tablet: 2-Column Grid */}
        <div className="hidden md:block lg:hidden">
          <div className="grid grid-cols-2 gap-6 justify-items-center">
            {arcaneStyles.map((style, index) => (
              <motion.div
                key={style.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
              >
                <StyleCard
                  style={style}
                  isSelected={selectedStyle === style.id}
                  onClick={() => onStyleSelect(style.id)}
                />
              </motion.div>
            ))}
          </div>
        </div>

        {/* Mobile: Single Column */}
        <div className="block md:hidden">
          <div className="space-y-4">
            {arcaneStyles.map((style, index) => (
              <motion.div
                key={style.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.05 }}
                className="w-full"
              >
                <StyleCard
                  style={style}
                  isSelected={selectedStyle === style.id}
                  onClick={() => onStyleSelect(style.id)}
                />
              </motion.div>
            ))}
          </div>
        </div>

        {/* Selected Style Indicator */}
        {selectedStyle && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-8 text-center"
          >
            <p className="font-manrope text-sm text-arcane-cyan">
              ✨ Selected: <span className="font-semibold">{arcaneStyles.find(s => s.id === selectedStyle)?.name}</span>
            </p>
          </motion.div>
        )}
      </div>
    </section>
  );
};

export default StylePicker;
