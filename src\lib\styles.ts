// Arcane Style Presets for AI Style Transfer

export interface StylePreset {
  id: string;
  name: string;
  description: string;
  prompt: string;
  preview?: string; // Optional preview image URL
}

export const arcaneStyles: StylePreset[] = [
  {
    id: 'eldritch-gothic',
    name: 'Eldritch Gothic',
    description: 'Transforms your photo into dark Gothic artwork with eldritch horror elements',
    prompt: 'Transform this image into a dark Gothic style with eldritch horror elements, featuring ornate architecture, dramatic shadows, and mysterious supernatural atmosphere'
  },
  {
    id: 'celestial-mystic',
    name: 'Celestial Mystic',
    description: 'Ethereal cosmic style with starlight and celestial magic',
    prompt: 'Transform this image into a celestial mystic style with starlight, cosmic energy, ethereal glows, and heavenly magical elements'
  },
  {
    id: 'shadow-realm',
    name: 'Shadow Realm',
    description: 'Dark dimensional style with shadow magic and void energy',
    prompt: 'Transform this image into a shadow realm style with dark dimensional portals, shadow magic, void energy, and mysterious dark atmosphere'
  },
  {
    id: 'crystal-enchantment',
    name: 'Crystal Enchantment',
    description: 'Magical crystal formations with prismatic light effects',
    prompt: 'Transform this image into a crystal enchantment style with magical crystal formations, prismatic light effects, and gem-like magical energy'
  },
  {
    id: 'runic-ancient',
    name: 'Runic Ancient',
    description: 'Ancient runic magic with mystical symbols and old power',
    prompt: 'Transform this image into a runic ancient style with mystical symbols, ancient runes, old magical power, and weathered stone textures'
  },
  {
    id: 'ethereal-dreamscape',
    name: 'Ethereal Dreamscape',
    description: 'Dreamlike magical realm with floating elements and soft mysticism',
    prompt: 'Transform this image into an ethereal dreamscape style with floating magical elements, soft mystical atmosphere, and dream-like surreal qualities'
  },
  {
    id: 'dark-sorcery',
    name: 'Dark Sorcery',
    description: 'Powerful dark magic with arcane energy and forbidden spells',
    prompt: 'Transform this image into a dark sorcery style with powerful dark magic, arcane energy, forbidden spells, and ominous magical atmosphere'
  },
  {
    id: 'cosmic-void',
    name: 'Cosmic Void',
    description: 'Deep space magic with nebulae and cosmic horror elements',
    prompt: 'Transform this image into a cosmic void style with deep space magic, swirling nebulae, cosmic horror elements, and infinite darkness'
  },
  {
    id: 'arcane-fire',
    name: 'Arcane Fire',
    description: 'Mystical flames and fire magic with burning magical energy',
    prompt: 'Transform this image into an arcane fire style with mystical flames, fire magic, burning magical energy, and ember-like glowing effects'
  },
  {
    id: 'frost-magic',
    name: 'Frost Magic',
    description: 'Ice and snow magic with crystalline frost and winter mysticism',
    prompt: 'Transform this image into a frost magic style with ice and snow magic, crystalline frost effects, and winter mystical atmosphere'
  },
  {
    id: 'nature-druid',
    name: 'Nature Druid',
    description: 'Natural magic with forest spirits and earth-based mysticism',
    prompt: 'Transform this image into a nature druid style with natural magic, forest spirits, earth-based mysticism, and organic magical elements'
  },
  {
    id: 'blood-moon',
    name: 'Blood Moon',
    description: 'Crimson lunar magic with dark red mystical energy',
    prompt: 'Transform this image into a blood moon style with crimson lunar magic, dark red mystical energy, and ominous moonlit atmosphere'
  },
  {
    id: 'golden-alchemy',
    name: 'Golden Alchemy',
    description: 'Alchemical transmutation with golden magical energy and ancient wisdom',
    prompt: 'Transform this image into a golden alchemy style with alchemical transmutation, golden magical energy, ancient wisdom symbols, and metallic mysticism'
  },
  {
    id: 'storm-elemental',
    name: 'Storm Elemental',
    description: 'Lightning and storm magic with electrical mystical energy',
    prompt: 'Transform this image into a storm elemental style with lightning magic, electrical mystical energy, storm clouds, and crackling power'
  },
  {
    id: 'void-walker',
    name: 'Void Walker',
    description: 'Interdimensional magic with portal energy and reality distortion',
    prompt: 'Transform this image into a void walker style with interdimensional magic, portal energy, reality distortion effects, and otherworldly atmosphere'
  }
];

export const getStyleById = (id: string): StylePreset | undefined => {
  return arcaneStyles.find(style => style.id === id);
};

export const getRandomStyle = (): StylePreset => {
  const randomIndex = Math.floor(Math.random() * arcaneStyles.length);
  return arcaneStyles[randomIndex];
};
