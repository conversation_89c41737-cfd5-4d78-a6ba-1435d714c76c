/** @type {import('tailwindcss').Config} */
export default {
  darkMode: 'class',
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Arcane Artify Design Tokens - v2.0
        arcane: {
          // Base Obsidian
          obsidian: '#0b0b12',
          // Accent Violet
          violet: '#7e3ff2',
          // Mystic Cyan
          cyan: '#38b2ac',
          // Ember Orange
          orange: '#d97706',
          // Legacy colors for backward compatibility
          primary: '#7e3ff2',
          secondary: '#38b2ac',
          dark: '#0b0b12',
          accent: '#7e3ff2',
          light: '#ede9fe',
        },
      },
      fontFamily: {
        // Headings: Cinzel, serif, letter-spacing 0.05em, weight 600
        cinzel: ['Cinzel', 'serif'],
        // Body: Manrope, sans serif, weight 400, line-height 1.6
        manrope: ['Manrope', 'sans-serif'],
        // Legacy
        arcane: ['Cinzel', 'serif'],
      },
      letterSpacing: {
        'arcane': '0.05em',
      },
      lineHeight: {
        'arcane': '1.6',
      },
      spacing: {
        // 8px base grid system
        '18': '4.5rem',   // 72px
        '22': '5.5rem',   // 88px
        '26': '6.5rem',   // 104px
        '30': '7.5rem',   // 120px
      },
      borderRadius: {
        'arcane': '16px',  // rounded-2xl equivalent
      },
      boxShadow: {
        'arcane': '0 10px 25px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'arcane-lg': '0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'arcane-xl': '0 25px 50px -12px rgba(0, 0, 0, 0.5)',
        'arcane-2xl': '0 25px 50px -12px rgba(126, 63, 242, 0.25)',
      },
      animation: {
        'float': 'float 3s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite',
        'shimmer': 'shimmer 2s ease-in-out infinite',
        'spin-slow': 'spin 30s linear infinite',
        'chroma-border': 'chroma-border 6s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        glow: {
          '0%, 100%': { boxShadow: '0 0 20px rgba(126, 63, 242, 0.3)' },
          '50%': { boxShadow: '0 0 30px rgba(126, 63, 242, 0.6)' },
        },
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'chroma-border': {
          '0%': { borderColor: '#7e3ff2' },
          '33%': { borderColor: '#38b2ac' },
          '66%': { borderColor: '#d97706' },
          '100%': { borderColor: '#7e3ff2' },
        },
      },
      backdropBlur: {
        'arcane': '12px',
      },
    },
  },
  plugins: [],
}