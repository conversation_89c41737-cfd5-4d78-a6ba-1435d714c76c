"use client";
import React from 'react';
import { motion } from 'framer-motion';

const HeroSection: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center text-center overflow-hidden">
      {/* Nebula Gradient Background */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: 'radial-gradient(circle at 50% 50%, #2c1460 0%, #0b0b12 80%)'
        }}
      >
        {/* Runic Overlay */}
        <div className="absolute inset-0 opacity-10">
          <svg className="w-full h-full" viewBox="0 0 400 400" fill="none">
            <defs>
              <pattern id="runicPattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
                {/* Runic Glyphs */}
                <path d="M20 20 L30 40 L40 20 M25 30 L35 30" stroke="#7e3ff2" strokeWidth="0.5" opacity="0.6"/>
                <path d="M60 25 L70 25 M65 20 L65 30 M70 20 L75 25 L70 30" stroke="#38b2ac" strokeWidth="0.5" opacity="0.4"/>
                <circle cx="25" cy="65" r="8" stroke="#d97706" strokeWidth="0.5" fill="none" opacity="0.3"/>
                <path d="M60 60 L80 60 M60 70 L80 70 M70 55 L70 75" stroke="#7e3ff2" strokeWidth="0.5" opacity="0.5"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#runicPattern)" />
          </svg>
        </div>
        {/* Floating Mystical Orbs */}
        <motion.div
          animate={{
            y: [0, -20, 0],
            opacity: [0.3, 0.7, 0.3],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/4 left-1/4 w-20 h-20 bg-arcane-violet/20 rounded-full blur-xl"
        />
        <motion.div
          animate={{
            y: [0, 15, 0],
            opacity: [0.2, 0.6, 0.2],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute top-1/2 right-1/4 w-24 h-24 bg-arcane-cyan/15 rounded-full blur-2xl"
        />
        <motion.div
          animate={{
            y: [0, -10, 0],
            opacity: [0.4, 0.8, 0.4],
            scale: [1, 1.15, 1]
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute bottom-1/3 left-1/2 w-16 h-16 bg-arcane-orange/25 rounded-full blur-lg"
        />

        {/* Rotating Crystal Frame Preview */}
        <motion.div
          animate={{
            rotate: 360
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/2 right-1/3 w-32 h-32 border-2 border-arcane-violet/30 rounded-full hidden lg:block"
        >
          <div className="absolute inset-2 border border-arcane-cyan/20 rounded-full">
            <div className="absolute inset-2 border border-arcane-orange/20 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-gradient-to-r from-arcane-violet to-arcane-cyan rounded-full blur-sm"></div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Hero Content - Desktop: Two-column, Mobile: Single column */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-8 grid grid-cols-1 lg:grid-cols-2 gap-8 items-center min-h-screen">
        {/* Left Column: Content */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center lg:text-left"
        >
          {/* Headline */}
          <motion.h1
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            className="font-cinzel text-3xl md:text-4xl lg:text-5xl font-semibold mb-4 text-arcane-violet tracking-arcane leading-tight"
          >
            Unleash the Arcane in Your Photos
          </motion.h1>

          {/* Subheadline */}
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="font-manrope text-lg text-arcane-cyan mb-8 leading-arcane max-w-xl mx-auto lg:mx-0"
          >
            Select a style, upload, and witness the magic
          </motion.p>

          {/* CTA Button */}
          <motion.button
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            whileHover={{
              scale: 1.05,
              backgroundColor: '#6b33d1'
            }}
            whileTap={{ scale: 0.98 }}
            className="rounded-2xl bg-arcane-violet py-3 px-6 text-white font-manrope font-semibold shadow-arcane-lg transition-all duration-300 hover:shadow-arcane-2xl focus:outline-none focus:ring-2 focus:ring-arcane-violet focus:ring-offset-2 focus:ring-offset-arcane-obsidian"
            onClick={() => document.getElementById('style-picker')?.scrollIntoView({ behavior: 'smooth' })}
            aria-label="Begin your magical transformation ritual"
          >
            Begin Your Ritual
          </motion.button>
        </motion.div>

        {/* Right Column: Crystal Frame Preview (Desktop only) */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="hidden lg:flex justify-center items-center"
        >
          <motion.div
            animate={{
              rotate: 360
            }}
            transition={{
              duration: 30,
              repeat: Infinity,
              ease: "linear"
            }}
            className="relative w-80 h-80 border-2 border-arcane-violet/40 rounded-full"
          >
            <div className="absolute inset-4 border border-arcane-cyan/30 rounded-full">
              <div className="absolute inset-4 border border-arcane-orange/30 rounded-full flex items-center justify-center">
                <div className="w-32 h-32 bg-gradient-to-br from-arcane-violet/20 via-arcane-cyan/20 to-arcane-orange/20 rounded-full backdrop-blur-sm flex items-center justify-center">
                  <span className="font-cinzel text-sm text-white/60 tracking-arcane">Preview</span>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;