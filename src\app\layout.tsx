import type { Metadata } from "next";
import { Cinzel_Decorative } from "next/font/google";
import "./globals.css";
import Link from "next/link";

const cinzel_decorative = Cinzel_Decorative({
  weight: ["400", "700", "900"],
  subsets: ["latin"],
  variable: "--font-cinzel-decorative",
});

export const metadata: Metadata = {
  title: "Arcane Artify",
  description: "Transform your images with dark-magical style transfers, powered by Google Gemini 2.0 Flash.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${cinzel_decorative.variable}`} style={{ background: '#000000' }}>
      <body
        className="bg-gradient-to-br from-arcane-dark via-purple-950 to-black text-arcane-light min-h-screen"
        style={{
          background: 'linear-gradient(135deg, #1e1b4b 0%, #581c87 50%, #000000 100%)',
          minHeight: '100vh',
          color: '#ede9fe'
        }}
      >
        <nav
          className="px-6 py-3 bg-black/40 backdrop-blur-md border-b border-arcane-primary/20 shadow-lg flex justify-between items-center sticky top-0 z-50"
          style={{
            background: 'rgba(0, 0, 0, 0.4)',
            backdropFilter: 'blur(12px)',
            borderBottom: '1px solid rgba(94, 42, 255, 0.2)'
          }}
        >
          <Link
            href="/"
            className="text-xl font-arcane font-bold text-arcane-primary hover:text-arcane-accent transition-colors duration-300"
            style={{ color: '#5e2aff' }}
          >
            Arcane Artify
          </Link>
          <div className="space-x-4">
            <Link
              href="/"
              className="text-arcane-light hover:text-arcane-accent transition-colors duration-200 font-medium text-sm"
              style={{ color: '#ede9fe' }}
            >
              Home
            </Link>
            <Link
              href="/gallery"
              className="text-arcane-light hover:text-arcane-accent transition-colors duration-200 font-medium text-sm"
              style={{ color: '#ede9fe' }}
            >
              Gallery
            </Link>
          </div>
        </nav>
        {children}
<script defer data-domain="arcaneartify.vercel.app" src="https://plausible.io/js/script.js"></script>
      </body>
    </html>
  );
}
