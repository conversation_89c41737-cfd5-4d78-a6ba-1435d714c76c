import type { Metadata } from "next";
import { Cinzel, Manrope } from "next/font/google";
import "./globals.css";
import Link from "next/link";

const cinzel = Cinzel({
  weight: ["400", "600", "700"],
  subsets: ["latin"],
  variable: "--font-cinzel",
});

const manrope = Manrope({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  variable: "--font-manrope",
});

export const metadata: Metadata = {
  title: "Arcane Artify",
  description: "Transform your images with dark-magical style transfers, powered by Google Gemini 2.0 Flash.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${cinzel.variable} ${manrope.variable}`} style={{ background: '#0b0b12' }}>
      <body
        className={`${manrope.className} bg-gradient-to-br from-arcane-obsidian via-purple-950 to-black text-arcane-light min-h-screen`}
        style={{
          background: 'radial-gradient(circle at 50% 50%, #2c1460 0%, #0b0b12 80%)',
          minHeight: '100vh',
          color: '#ede9fe'
        }}
      >
        <nav className="w-full z-50 bg-arcane-obsidian/80 backdrop-blur-lg fixed top-0 left-0 border-b border-arcane-violet/20">
          <div className="max-w-7xl mx-auto px-6 py-4 flex justify-between items-center">
            <Link
              href="/"
              className={`${cinzel.className} text-xl font-semibold text-arcane-violet hover:text-arcane-cyan transition-colors duration-300 tracking-arcane`}
            >
              Arcane Artify
            </Link>
            <div className="flex space-x-6">
              <Link
                href="/"
                className={`${manrope.className} text-base text-white/80 hover:text-arcane-cyan transition-colors duration-200 font-medium`}
              >
                🔮 Home
              </Link>
              <Link
                href="/gallery"
                className={`${manrope.className} text-base text-white/80 hover:text-arcane-cyan transition-colors duration-200 font-medium`}
              >
                🗝️ Gallery
              </Link>
              <Link
                href="/pricing"
                className={`${manrope.className} text-base text-white/80 hover:text-arcane-cyan transition-colors duration-200 font-medium`}
              >
                🪙 Pricing
              </Link>
              <Link
                href="/docs"
                className={`${manrope.className} text-base text-white/80 hover:text-arcane-cyan transition-colors duration-200 font-medium`}
              >
                📜 Docs
              </Link>
            </div>
          </div>
        </nav>
        {children}
<script defer data-domain="arcaneartify.vercel.app" src="https://plausible.io/js/script.js"></script>
      </body>
    </html>
  );
}
