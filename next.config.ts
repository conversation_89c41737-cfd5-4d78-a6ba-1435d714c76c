import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },
    ],
    // Allow data URLs for base64 images generated by Gemini
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
  },
  /* config options here */
};

export default nextConfig;
