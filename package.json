{"name": "arcane-artify", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "test": "vitest", "test:e2e": "playwright test", "lint": "next lint", "lighthouse-audit": "npx lighthouse https://arcane-artify.vercel.app/ --output=json --output-path=lighthouse-results.json --only-categories=performance,accessibility,best-practices,seo", "uptime-check": "node scripts/uptime-monitor.cjs"}, "dependencies": {"@google/generative-ai": "0.13.0", "@supabase/supabase-js": "2.46.2", "axios": "^1.9.0", "chrome-launcher": "^1.2.0", "dotenv": "^16.5.0", "framer-motion": "^12.15.0", "lighthouse": "^12.6.0", "lucide-react": "^0.511.0", "motion": "12.10.5", "next": "15.3.2", "patch-package": "^8.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-masonry-css": "^1.0.16", "stripe": "12", "tailwind-variants": "^1.0.0", "zod": "^3.25.34", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.5.0", "eslint": "^9", "eslint-config-next": "15.3.2", "jsdom": "^26.1.0", "playwright": "^1.52.0", "tailwindcss": "^4.1.2", "typescript": "^5", "vitest": "^3.1.4"}}