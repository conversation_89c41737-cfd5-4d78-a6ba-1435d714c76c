/* Dark Purple Mystic Theme - v2.0 */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-cinzel-decorative: "Cinzel Decorative";
  }
}

body {
  font-family: var(--font-cinzel-decorative), serif;
  background: linear-gradient(135deg, #1e1b4b 0%, #581c87 50%, #000000 100%) !important;
  background-attachment: fixed !important;
  min-height: 100vh;
  color: #ede9fe !important;
}

html {
  background: #000000 !important;
}

/* Ensure main elements have proper dark background */
main {
  background: transparent !important;
}

/* Override any conflicting styles */
* {
  box-sizing: border-box;
}

/* Force dark theme */
.bg-white, .bg-gray-50, .bg-gray-100 {
  background: transparent !important;
}

/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-arcane-primary {
  scrollbar-color: #5e2aff transparent;
}

.scrollbar-track-transparent {
  scrollbar-track-color: transparent;
}

/* Webkit scrollbar styles for better browser support */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #5e2aff;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #c084fc;
}

/* Custom Masonry Grid Styles */
.my-masonry-grid {
  display: -webkit-box; /* Not needed if autoprefixing */
  display: -ms-flexbox; /* Not needed if autoprefixing */
  display: flex;
  margin-left: -30px; /* gutter size offset */
  width: auto;
}
.my-masonry-grid_column {
  padding-left: 30px; /* gutter size */
  background-clip: padding-box;
}

/* Style your items */
.my-masonry-grid_column > div { /* change div to all your item classes for masonry */
  margin-bottom: 30px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .scrollbar-thin::-webkit-scrollbar {
    width: 4px;
  }

  /* Reduce masonry grid spacing on mobile */
  .my-masonry-grid {
    margin-left: -15px;
  }
  .my-masonry-grid_column {
    padding-left: 15px;
  }
  .my-masonry-grid_column > div {
    margin-bottom: 15px;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(94, 42, 255, 0.3); }
  50% { box-shadow: 0 0 30px rgba(94, 42, 255, 0.6); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* Backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-md {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible {
  outline: 2px solid #5e2aff;
  outline-offset: 2px;
}

/* Loading spinner */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
