/* Arcane Artify Design System - v2.0 */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-cinzel: "Cinzel";
    --font-manrope: "Manrope";

    /* Design Tokens */
    --color-obsidian: #0b0b12;
    --color-violet: #7e3ff2;
    --color-cyan: #38b2ac;
    --color-orange: #d97706;

    /* Motion 12 Spring Configuration */
    --spring-damping: 20;
    --spring-stiffness: 150;
  }

  /* Typography Defaults */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-cinzel);
    font-weight: 600;
    letter-spacing: 0.05em;
  }

  body {
    font-family: var(--font-manrope);
    font-weight: 400;
    line-height: 1.6;
  }
}

/* Global body styles are now handled in layout.tsx */

/* Ensure main elements have proper dark background */
main {
  background: transparent !important;
}

/* Override any conflicting styles */
* {
  box-sizing: border-box;
}

/* Force dark theme */
.bg-white, .bg-gray-50, .bg-gray-100 {
  background: transparent !important;
}

/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-arcane-primary {
  scrollbar-color: #5e2aff transparent;
}

.scrollbar-track-transparent {
  scrollbar-track-color: transparent;
}

/* Webkit scrollbar styles for better browser support */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #5e2aff;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #c084fc;
}

/* Custom Masonry Grid Styles */
.my-masonry-grid {
  display: -webkit-box; /* Not needed if autoprefixing */
  display: -ms-flexbox; /* Not needed if autoprefixing */
  display: flex;
  margin-left: -30px; /* gutter size offset */
  width: auto;
}
.my-masonry-grid_column {
  padding-left: 30px; /* gutter size */
  background-clip: padding-box;
}

/* Style your items */
.my-masonry-grid_column > div { /* change div to all your item classes for masonry */
  margin-bottom: 30px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .scrollbar-thin::-webkit-scrollbar {
    width: 4px;
  }

  /* Reduce masonry grid spacing on mobile */
  .my-masonry-grid {
    margin-left: -15px;
  }
  .my-masonry-grid_column {
    padding-left: 15px;
  }
  .my-masonry-grid_column > div {
    margin-bottom: 15px;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom animations with new design tokens */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(126, 63, 242, 0.3); }
  50% { box-shadow: 0 0 30px rgba(126, 63, 242, 0.6); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes chroma-border {
  0% { border-color: var(--color-violet); }
  33% { border-color: var(--color-cyan); }
  66% { border-color: var(--color-orange); }
  100% { border-color: var(--color-violet); }
}

@keyframes progress-slide {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

.animate-chroma-border {
  animation: chroma-border 6s ease-in-out infinite;
}

.animate-progress-slide {
  animation: progress-slide 2s linear infinite;
}

/* Backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-md {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

/* Focus styles for accessibility - WCAG 2.2-AA compliant */
button:focus-visible,
input:focus-visible,
a:focus-visible,
[role="button"]:focus-visible {
  outline: 2px solid var(--color-violet);
  outline-offset: 2px;
  border-radius: 4px;
}

/* 3D Transform utilities for StyleCard flip effect */
.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Disable 3D transforms for reduced motion */
  .preserve-3d {
    transform-style: flat;
  }
}

/* Loading spinner */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
