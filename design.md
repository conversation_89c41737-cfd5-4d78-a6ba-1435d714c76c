# Arcane Artify Design System v2.0

## Overview
Arcane Artify uses a dark-magical aesthetic with mystical elements, glassmorphic design patterns, and an 8px grid system. The design reinforces an arcane, ritualistic vibe through nebula gradients, runic overlays, and carefully crafted animations.

## Color Palette

### Primary Colors
- **Base Obsidian**: `#0b0b12` - Primary background color
- **Accent Violet**: `#7e3ff2` - Primary accent and interactive elements
- **Mystic Cyan**: `#38b2ac` - Secondary accent and hover states
- **Ember Orange**: `#d97706` - Tertiary accent for warnings and highlights

### Usage Guidelines
- Use Base Obsidian for backgrounds and containers
- Accent Violet for primary buttons, links, and focus states
- Mystic Cyan for hover states and secondary actions
- Ember Orange sparingly for warnings, errors, or special highlights

## Typography

### Font Families
- **Headings**: Cinzel, serif
  - Weight: 600 (semibold)
  - Letter-spacing: 0.05em
  - Usage: All h1-h6 elements, brand names, section titles
  
- **Body Text**: Manrope, sans-serif
  - Weight: 400 (regular)
  - Line-height: 1.6
  - Usage: Paragraphs, descriptions, UI text

### Font Scale
- **Display**: 3xl-5xl (48px-72px) - Hero headlines
- **Heading**: xl-2xl (20px-32px) - Section titles
- **Body**: base-lg (16px-18px) - Main content
- **Caption**: sm-xs (12px-14px) - Metadata, labels

## Spacing & Grid

### 8px Base Grid
All spacing follows 8px increments:
- **xs**: 8px (2 units)
- **sm**: 16px (4 units)  
- **md**: 24px (6 units)
- **lg**: 32px (8 units)
- **xl**: 40px (10 units)
- **2xl**: 48px (12 units)

### Container Widths
- **Max Width**: 1280px
- **Padding**: 24px (mobile), 32px (tablet), 48px (desktop)

## Border Radius
- **Standard**: 16px (rounded-2xl) for cards and buttons
- **Small**: 8px (rounded-lg) for form elements
- **Large**: 24px (rounded-3xl) for major containers

## Shadows
- **Default**: `shadow-arcane` - Standard depth
- **Large**: `shadow-arcane-lg` - Elevated elements
- **Extra Large**: `shadow-arcane-xl` - Modals, overlays
- **Colored**: `shadow-arcane-2xl` - Special emphasis with violet glow

## Motion & Animation

### Spring Configuration (Motion 12)
- **Damping**: 20
- **Stiffness**: 150
- **Duration**: 300-500ms for most interactions

### Animation Types
- **Float**: Subtle vertical movement for mystical elements
- **Glow**: Pulsing shadow effect for interactive elements
- **Shimmer**: Loading state animation
- **Chroma Border**: Color-cycling border animation
- **Progress Slide**: Continuous gradient movement

### Accessibility
- All animations respect `prefers-reduced-motion`
- Fallback to simple fade transitions when motion is reduced

## Component Patterns

### Glassmorphic Cards
```css
background: rgba(255, 255, 255, 0.1);
backdrop-filter: blur(12px);
border: 1px solid rgba(255, 255, 255, 0.2);
border-radius: 16px;
```

### Interactive Elements
- **Hover**: Scale 1.05, enhanced shadow
- **Focus**: 2px violet outline with 2px offset
- **Active**: Scale 0.98

### Button Styles
- **Primary**: Violet background, white text
- **Secondary**: Cyan background, black text  
- **Tertiary**: Orange background, black text
- **Ghost**: Transparent background, colored border

## Responsive Breakpoints

### Mobile (≤ 480px)
- Single column layouts
- Smaller typography scale
- Touch-friendly 44px minimum tap targets
- Reduced spacing and padding

### Tablet (481px - 1024px)
- Two-column grids where appropriate
- Medium typography scale
- Balanced spacing

### Desktop (≥ 1025px)
- Multi-column layouts
- Full typography scale
- Maximum spacing and visual hierarchy

## Accessibility Standards

### WCAG 2.2-AA Compliance
- **Color Contrast**: Minimum 4.5:1 for normal text, 3:1 for large text
- **Keyboard Navigation**: All interactive elements focusable
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Motion**: Respects user motion preferences

### Focus Management
- Visible focus indicators on all interactive elements
- Logical tab order
- Skip links for main content areas

## Implementation Notes

### CSS Custom Properties
```css
:root {
  --color-obsidian: #0b0b12;
  --color-violet: #7e3ff2;
  --color-cyan: #38b2ac;
  --color-orange: #d97706;
  --spring-damping: 20;
  --spring-stiffness: 150;
}
```

### Tailwind Configuration
All design tokens are configured in `tailwind.config.mjs` with proper naming conventions and utility classes.

### Component Architecture
- Reusable React components with consistent prop interfaces
- Tailwind utility classes preferred over custom CSS
- Motion 12 for all animations with proper fallbacks
