import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from "@google/generative-ai";
import { NextResponse } from "next/server";

export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const image = formData.get('image') as File;
    const style = formData.get('style') as string;

    if (!image || !style) {
      return NextResponse.json({ error: 'Missing image or style' }, { status: 400 });
    }

    // Convert image to base64
    const buffer = await image.arrayBuffer();
    const base64Image = Buffer.from(buffer).toString('base64');

    // Initialize Gemini with the experimental model that supports image generation
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash-exp",
      generationConfig: {
        responseModalities: ["Text", "Image"]
      }
    });

    // Create a detailed prompt for image transformation
    const prompt = `Transform this image to apply a ${style} artistic style.
    Create a new image that maintains the core composition and subject matter of the original,
    but applies the visual characteristics, color palette, textures, and artistic techniques
    associated with the ${style} style. The transformation should be dramatic and clearly
    show the artistic style while preserving the recognizable elements of the original image.`;

    // Generate transformed image
    const result = await model.generateContent({
      contents: [
        {
          role: "user",
          parts: [
            { text: prompt },
            { inlineData: { data: base64Image, mimeType: image.type } }
          ]
        }
      ],
      safetySettings: [{
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH
      }]
    });

    // Extract the generated image from the response
    const response = result.response;
    let transformedImageData = null;

    // Look for image data in the response parts
    for (const candidate of response.candidates || []) {
      for (const part of candidate.content?.parts || []) {
        if (part.inlineData && part.inlineData.mimeType?.startsWith('image/')) {
          transformedImageData = part.inlineData.data;
          break;
        }
      }
      if (transformedImageData) break;
    }

    if (transformedImageData) {
      // Return the base64 image data as a data URL
      const mimeType = response.candidates?.[0]?.content?.parts?.find(p => p.inlineData)?.inlineData?.mimeType || 'image/png';
      const dataUrl = `data:${mimeType};base64,${transformedImageData}`;
      return NextResponse.json({ image: dataUrl });
    } else {
      // Fallback if no image was generated
      console.log('No image generated, response:', response.text());
      return NextResponse.json({
        error: 'No image was generated. The model may have returned text instead of an image.',
        fallback: 'https://picsum.photos/500/500?random=1'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in image generation:', error);
    return NextResponse.json({
      error: 'Internal server error during image generation',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}